# UGC Orchestrator - TODO Progress

This document tracks the current MVP scope and progress across Sprints, reflecting what has been implemented in the monorepo and what remains.

## Legend
- [x] Completed
- [-] In Progress
- [ ] Pending

## Sprint 1 — Foundation (Monorepo, DB, Core APIs, Minimal UI)

1. Monorepo scaffolding (npm workspaces; apps/web, packages/db, packages/agents, packages/shared)
   - [x] Root workspaces and Node 20+ engines in package.json
   - [x] apps/web Next.js app (App Router) running at http://localhost:3000
   - [x] Path aliases via tsconfig.base.json and per-package tsconfig.json
   - [-] Standard lint/test/CI setup

2. Infrastructure (Local Dev)
   - [x] Local Postgres 16 via Homebrew
   - [x] DB connection utility with <PERSON><PERSON><PERSON> (node-postgres driver)
   - [x] Drizzle config (drizzle-kit) CJS CLI compatibility
   - [x] Migrations generation and application
   - [x] Seed script (.env + tsx) to bootstrap default workspace

3. Data Model (Initial Entities)
   - [x] Workspace, User, Membership/Role
   - [x] Integration
   - [x] Campaign
   - [x] Brief
   - [x] Approval
   - [x] AuditLog
   - [-] Validation rules, indexes, and referential hardening

4. API Endpoints (MVP)
   - [x] POST /api/campaigns — create campaign under a workspace
   - [x] POST /api/briefs (generate) — stub generation output saved to DB
   - [x] POST /api/briefs/:id/approve — record approval status
   - [x] GET /api/events/stream — SSE hello + heartbeat
   - [-] Replace relative imports with @db/* path aliases
   - [ ] OpenAPI documentation for endpoints

5. Minimal UI
   - [x] Create Campaign page (apps/web/src/app/campaigns/page.tsx) with form
   - [x] Briefs page (apps/web/src/app/briefs/page.tsx) with “Generate Brief” + “Approve” actions and SSE subscription
   - [ ] Basic layout and navigation between pages
   - [ ] Loading/error states hardened and styled
   - [ ] Basic form validation

6. Developer Experience
   - [x] Seed script: npm run seed
   - [-] Shortcut scripts: dev:web, typecheck:web, db:seed (wrappers)
   - [ ] Prettier + ESLint finalized and project-wide

## Sprint 2 — Avatar Catalog & Pipeline Entities

1. Licensed Avatar Catalog
   - [ ] Schemas: Creator, Avatar, AvatarLicense
   - [ ] GET /api/avatars with filters (e.g., language, demographic, tone)
   - [ ] Catalog UI and selection

2. AI Pipeline Entities
   - [ ] RenderScript
   - [ ] TTSOutput
   - [ ] LipSyncJob
   - [ ] CompositionJob
   - [ ] Migrations for above

3. Pipeline Orchestration (initial stubs)
   - [ ] Queues + events scaffolding for tts, lipsync, compose
   - [ ] DLQ & retries
   - [ ] Stage events for observability

4. Provider Adapters (stubs)
   - [ ] TTSAdapter (SSML support)
   - [ ] LipSyncAdapter (vendor + forced-alignment fallback)
   - [ ] Compositor (ffmpeg templates)

## Sprint 3 — Launch & Tests

1. Entities
   - [ ] LaunchBundle
   - [ ] Experiment
   - [ ] PlatformPost
   - [ ] PerformanceMetric

2. APIs
   - [ ] POST /api/launch/plan
   - [ ] POST /api/experiments/:id/launch
   - [ ] Sandbox adapters for TikTok/Instagram posting (dry-run)

3. Analytics & Recommendations
   - [ ] Analytics loop, metrics ingestion, recommendations scaffold

## Compliance, Rights & QA (Ongoing across Sprints)
- [ ] QA & Compliance checks (claims whitelist, disclosure, safe areas, loudness)
- [ ] POST /api/assets/:id/qa and basic UI
- [ ] Rights checks (AvatarLicense scope, RightsWindow validations) and pre-launch verifications
- [ ] Expiry alerts and guardrails

## Observability (Ongoing)
- [ ] Standardized event bus and correlation IDs
- [ ] Job dashboards per stage
- [ ] Minimal tracing/logging policy

## Documentation
- [-] TechnicalReport.md (v1.2 updated to reflect avatar pipeline and licensed model)
- [-] UserStoriesAndTasks.md (v1.2 updated)
- [ ] OpenAPI for MVP endpoints
- [ ] ERD and runbooks (local dev, migrations, seed, common flows)
- [ ] Composition presets for video overlays

---

# Current Implementation Notes

- Local DB:
  - DATABASE_URL=postgres://postgres:postgres@localhost:5432/ugc_gen
  - Migrations generated and applied successfully.
  - Seed inserts default workspace with slug “acme”.

- API routes implemented:
  - apps/web/src/app/api/campaigns/route.ts
  - apps/web/src/app/api/briefs/route.ts
  - apps/web/src/app/api/briefs/[id]/approve/route.ts
  - apps/web/src/app/api/events/stream/route.ts

- UI pages:
  - apps/web/src/app/campaigns/page.tsx
  - apps/web/src/app/briefs/page.tsx

- DB package:
  - packages/db/src/schema.ts
  - packages/db/src/client.ts
  - packages/db/drizzle.config.ts (CJS for CLI)

---

# Immediate Next Actions

1) DX polish
   - Replace relative imports in API routes with @db/* aliases.
   - Add scripts: dev:web, typecheck:web, db:seed (wrapping existing commands).

2) OpenAPI Docs
   - Define schemas and request/response examples for MVP endpoints.

3) UI Navigation
   - Add simple navigation between Home → Campaigns → Briefs.

4) Data validations
   - Tighten column constraints and add helpful indexes.

5) Agents (Sprint 1 placeholder)
   - Add packages/agents initial scaffold with briefGenerator types and a stubbed run().

---

# Runbook Quick Commands

- Start dev server:
  - npm run dev -w apps/web

- Typecheck:
  - npm run typecheck -w apps/web

- Database (local):
  - DATABASE_URL=postgres://postgres:postgres@localhost:5432/ugc_gen npm run db:generate
  - DATABASE_URL=postgres://postgres:postgres@localhost:5432/ugc_gen npm run db:migrate

- Seed workspace:
  - npm run seed

- Smoke test:
  - curl -X POST http://localhost:3000/api/campaigns -H "content-type: application/json" -d '{ "name":"First Campaign","workspaceSlug":"acme","objective":"awareness" }'
  - curl -X POST http://localhost:3000/api/briefs -H "content-type: application/json" -d '{ "campaignId": 1, "title": "UGC Concepts v0" }'
  - curl -X POST http://localhost:3000/api/briefs/1/approve -H "content-type: application/json" -d '{ "status":"approved","note":"Looks good","userId":1 }'
