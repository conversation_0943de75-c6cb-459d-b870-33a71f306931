# UGC Orchestrator — User Stories and Implementation Tasks

Version: 1.2  
Owner: Product & Engineering  
Status: Draft

Changelog
- v1.2: Integrated Avatar Script-to-Video pipeline and replaced marketplace sourcing with:
  - A) Creator Avatar Onboarding (internal) — creators license avatars to platform.
  - B) UGC using Licensed Avatars — brand users select approved avatars; system renders script-to-video (TTS → lip-sync → composition).
  - Added RenderScript, TTSOutput, LipSyncJob, CompositionJob entities and pipeline stages; updated APIs, workflows, acceptance criteria.
- v1.1: Introduced licensed avatar catalog model (Avatar, AvatarLicense). Removed user-facing sourcing/agreements.
- v1.0: Initial stories.

## Epics Overview
- E1: Onboarding & Workspace Setup
- E2: Campaign & Brief Generation
- E3: Licensed Avatar Catalog (replaces marketplace sourcing)
- E4: AI UGC Generation (Script-to-Video Pipeline)
- E5: QA & Compliance
- E6: Launch & Micro-Testing
- E7: Analytics & Recommendations
- E8: Rights, Payments & Audit
- E9: Platform & Observability

Each epic includes user stories with acceptance criteria and implementation tasks.

---

## E1: Onboarding & Workspace Setup

US-1: As a user, I can create a workspace and invite teammates so we can collaborate with roles.
- Acceptance Criteria:
  - Create workspace with name and plan tier.
  - Invite users by email; roles: owner, admin, editor, reviewer.
  - RBAC enforced on protected actions.
- Tasks:
  - BE: Workspace, User, Membership, Role (schema, migrations).
  - BE: AuthN/AuthZ middleware; RBAC guards.
  - FE: Workspace creation & team management UIs.
  - BE: Invitations API (token/email stub).
  - QA: Role matrix tests.

US-2: As an admin, I can connect integrations (Shop/IG/TikTok/Storage) required for downstream features.
- Acceptance Criteria:
  - Integration records with type/status/auth metadata.
  - Secure token storage; connect/disconnect; health-check.
- Tasks:
  - BE: Integration entity; encrypted tokens.
  - BE: OAuth/API key routes; health-check endpoints.
  - FE: Integrations dashboard.
  - SEC: Secrets handling; logs masking.

---

## E2: Campaign & Brief Generation

US-3: As a marketer, I can create a campaign selecting product(s), goals, channels, budget, and human-vs-AI mix.
- Acceptance Criteria:
  - Campaign form with validations.
  - Status = draft on creation.
- Tasks:
  - BE: Campaign schema; POST /api/campaigns.
  - FE: Campaign create/edit UI.
  - QA: CRUD and validation tests.

US-4: As a marketer, I can generate a brief from product feed and brand rules and edit before approval.
- Acceptance Criteria:
  - Agent generates a draft with angles, scripts, hooks, captions, hashtags, disclosures.
  - Event stream shows progress/errors.
  - Edit, save; request approval; downstream gated by Approval.
- Tasks:
  - BE: Brief entity (body JSONB, approval_status).
  - AGENT: briefGenerator with prompt templates and guardrails.
  - BE: POST /api/briefs/generate; GET /api/events/stream (SSE).
  - FE: Brief editor UI.
  - BE: Approvals: POST /api/briefs/:id/approve.
  - OBS: AuditLog for edits/approvals.
  - TEST: Agent invocation, SSE integration.

---

## E3: Licensed Avatar Catalog

US-5: As a brand user, I can browse a catalog of approved avatars and filter by language, tone, niche tags, and constraints.
- Acceptance Criteria:
  - GET /api/avatars returns only approved & active avatars with public-safe metadata.
  - Filters for languages, tones, tags; pagination.
- Tasks:
  - BE: Avatar, AvatarLicense entities; visibility constraints.
  - BE: GET /api/avatars with filters; caching.
  - FE: Avatar catalog UI with filters and detail view.
  - TEST: Catalog filter tests; license visibility constraints.

US-6 (Internal): As an ops admin, I can onboard creators’ avatars by verifying identity, capturing consent/license, and activating avatars.
- Acceptance Criteria:
  - Creator register; avatar submit; verification; license activation lifecycle.
  - Only active avatars appear in catalog.
- Tasks:
  - BE: Creator (internal), Avatar, AvatarLicense schemas; status transitions.
  - BE: Internal routes: /internal/creators, /internal/avatars, /internal/licenses.
  - FE (admin): Onboarding forms; review/approve screens.
  - SEC: RBAC isolation; PII isolation; audit logs.
  - TEST: Lifecycle tests; expiry/renewal schedule.

---

## E4: AI UGC Generation (Script-to-Video Pipeline)

US-7: As a marketer, I can configure an AI cast by selecting an approved avatar, voice/language, tone, and number of variants.
- Acceptance Criteria:
  - AICast bound to Avatar; validation of constraints (languages/tones).
- Tasks:
  - BE: AICast references Avatar; validations.
  - FE: Cast configuration UI within campaign context.
  - TEST: Validation and persistence.

US-8: As a marketer, I can provide a custom script (or use the auto-generated script) and have the avatar read it in the final video.
- Acceptance Criteria:
  - System accepts text or SSML; can also auto-generate from Brief.
  - Pipeline stages: TTS → lip-sync → composition; status visible in UI.
  - Fallbacks: forced alignment if no visemes; captions-only + b-roll if lip-sync fails.
- Tasks:
  - BE: New entities:
    - RenderScript { brief_id, ai_cast_id, text, ssml?, segments[], timing_prefs, style, target_aspect_ratios[] }
    - TTSOutput { render_script_id, audio_url, viseme_track?, phoneme_track?, duration_ms }
    - LipSyncJob { render_script_id, avatar_id, audio_url, model_params, status, output_track_url, logs }
    - CompositionJob { lip_sync_job_id, overlays, layout_preset, loudness_target, aspect_ratios[], status, output_asset_ids[] }
  - BE: POST /api/ai-ugc/render accepts { briefId, aiCastId, renderScript?, variantSpec? }; emits stage events.
  - AGENT: aiUGCRenderer implements stages and fallbacks.
  - FE: Render console UI with per-stage progress and logs.
  - WORKER: Queues for tts, lipsync, compose; DLQs and retries.
  - OBS: Emit tts_started/completed, lipsync_started/completed, compose_started/completed, asset_ready events.

US-9: As a marketer, I can include on-screen text, captions, product shots, and brand layouts automatically synchronized to the speech.
- Acceptance Criteria:
  - Captions burned-in or sidecar files; on-screen text aligned to sentence timings.
  - Safe-area templates for IG/TikTok; loudness normalized to target.
- Tasks:
  - FE: Overlay editor presets tied to brief sections.
  - BE: Composition templates and ffmpeg pipeline (or vendor).
  - QA: Caption accuracy and safe-area validation.

---

## E5: QA & Compliance

US-10: As a reviewer, I can run automated QA on assets for brand voice, typography, framing, claims, disclosures, music rights, and loudness.
- Acceptance Criteria:
  - QAReport with checks, result (pass/fail/needs_review), issues, auto_fixes.
  - Recheck possible after fixes.
- Tasks:
  - AGENT: qaCompliance sub-agent; validators and heuristics.
  - BE: QAReport entity; POST /api/assets/:id/qa.
  - FE: QA dashboard with diffs/suggestions.
  - TEST: Validation fixtures and thresholds.

US-11: As a reviewer, I can approve or reject assets after QA, gating launch readiness.
- Acceptance Criteria:
  - Approval entity updates; asset flagged as approved_for_launch.
- Tasks:
  - BE: Approval flow for assets; transition checks.
  - FE: Approve/reject UI with comments.
  - AUDIT: Approval snapshots captured.

---

## E6: Launch & Micro-Testing

US-12: As a marketer, I can assemble launch-ready bundles with assets, captions, hashtags, sounds, and tracking params per platform.
- Acceptance Criteria:
  - LaunchBundle built and validated per platform policies and aspect ratios.
- Tasks:
  - BE: LaunchBundle; POST /api/launch/plan.
  - FE: Bundle builder UI; platform presets.
  - VAL: Policy checks before scheduling.

US-13: As a marketer, I can schedule micro A/B/C tests and monitor posting status and early metrics.
- Acceptance Criteria:
  - Experiment created with variants; scheduled posting to sandbox or manual export.
  - Status updates and permalinks available.
- Tasks:
  - BE: Experiment, PlatformPost; POST /api/experiments/:id/launch.
  - ADAPTERS: InstagramGraphAdapter, TikTokAdapter.
  - WEBHOOKS: /api/webhooks/platform-posts; signature validation.
  - FE: Experiments UI and live status.

---

## E7: Analytics & Recommendations

US-14: As a marketer, I can view performance metrics (retention, CTR, CPA/ROAS, sentiment) per asset/experiment and over time.
- Acceptance Criteria:
  - Dashboards with filters; time-series charts.
- Tasks:
  - BE: PerformanceMetric ingestion/storage; aggregation endpoints.
  - FE: Metrics dashboard; charts and export.
  - PIPE: ETL from platform APIs (scheduled jobs).

US-15: As a marketer, I get recommendations for next variants, hooks, CTAs, and budget shifts, and I can approve to execute.
- Acceptance Criteria:
  - Recommendation records with rationale and predicted impact.
  - Approval gates execution; track executed changes.
- Tasks:
  - AGENT: analyticsLoop sub-agent (heuristics/LLM prompts).
  - BE: Recommendation entity; approval endpoint.
  - FE: Recommendations UI with apply/track.

---

## E8: Rights, Payments & Audit

US-16: As an operator, I can track license windows per avatar and rights windows per asset, with renewal warnings.
- Acceptance Criteria:
  - AvatarLicense lifecycle; RightsWindow validations at render and launch.
- Tasks:
  - BE: AvatarLicense, RightsWindow entities; schedulers for renewals.
  - FE: Rights dashboard; renewal CTAs.
  - VAL: Pre-render and pre-launch rights checks.

US-17: As finance/admin, I can configure and track optional creator royalties tied to avatar usage.
- Acceptance Criteria:
  - Payment records; due/paid statuses; usage reporting.
- Tasks:
  - BE: Payment entity; royalty calculation job (optional MVP).
  - FE: Payments view; CSV export.
  - SEC: Role-based access for finance.

US-18: As a compliance officer, I can audit significant actions and approvals.
- Acceptance Criteria:
  - AuditLog with actor, entity, action, timestamp, diff_snapshot.
- Tasks:
  - BE: AuditLog middleware/hooks.
  - FE: Audit timeline per campaign/entity.
  - OPS: Log retention policy.

---

## E9: Platform & Observability

US-19: As an engineer, I can see real-time job progress across pipeline stages and approvals timeline.
- Acceptance Criteria:
  - SSE/WebSocket streams keyed by jobId/campaignId.
  - Timeline UI across TTS, lip-sync, composition, QA, launch.
- Tasks:
  - BE: Event bus emitting standardized events.
  - FE: Unified timeline component.
  - OBS: Correlation IDs across logs and events.

US-20: As an operator, I can monitor system health: per-stage queues, throughput, error rates, and API quotas.
- Acceptance Criteria:
  - Health dashboard with alerts for stuck jobs, DLQ spikes, quota thresholds.
- Tasks:
  - OBS: Metrics collection; dashboards; alert rules.
  - OPS: Runbooks for common failures.

---

## Cross-Cutting Acceptance Criteria
- Security: Tokens encrypted at rest; no tokens in client; signed URLs for media; RBAC isolation for internal onboarding.
- Privacy: PII minimization; creator PII isolated in internal schema; public catalog shows only safe fields.
- Reliability: Idempotent handlers; retries with backoff; DLQ; transactional writes where applicable.
- Performance: P95 API latency targets; per-stage queue SLAs.
- Accessibility: WCAG AA for UIs; captions emphasized for sound-off viewers.
- Documentation: API schemas, ERD, runbooks, and pipeline playbooks.

---

## Release Plan Mapping
- M1 (Briefs MVP): US-3, US-4, US-19 (timeline base), US-18 (audit subset).
- M2 (Avatar Catalog + Pipeline Base): US-5, US-6 (internal), US-7, US-8, US-9, US-16 (pre-render checks).
- M3 (Launch & Tests): US-12, US-13.
- M4 (Analytics Loop): US-14, US-15.
- M5 (Onboarding Hardening & Royalties): US-6 advanced, US-17, US-16 renewals, reporting.

---

## Backlog / Future Considerations
- Multi-armed bandit for creative iteration and budget allocation.
- Gesture control API and beat-aligned overlay choreography.
- Automated caption localization and TTS dubbing.
- Billing and quotas per workspace; overage alerts.
- SOC2 roadmap and audit evidence automation.
