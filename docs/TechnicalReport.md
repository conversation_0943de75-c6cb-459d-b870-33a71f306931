# UGC Orchestrator — Technical Report

Version: 1.2  
Owner: Engineering  
Status: Draft

Changelog
- v1.2: Integrated “Avatar Script-to-Video Pipeline” details into Architecture, Data Model, APIs, and Workflows:
  - Added RenderScript, TTSOutput, LipSyncJob, CompositionJob entities and pipeline stages.
  - Expanded aiUGCRenderer responsibilities and vendor-agnostic fallbacks (forced alignment, captions-only fallback).
  - Added preflight claim checks and viseme/phoneme timing usage for better lip-sync and overlays.
- v1.1: Replaced marketplace creator sourcing with two flows (Creator Avatar Onboarding; UGC using Licensed Avatars). Introduced Avatar and AvatarLicense.
- v1.0: Initial report.

## 1. Executive Summary
The UGC Orchestrator converts product information and brand guidelines into launch-ready social content for TikTok and Instagram by combining human-licensed avatars and AI-generated UGC. Human creators onboard their avatars via a dedicated flow and license them to the platform. Brand users select from these pre-approved avatars to generate content. The system implements human-in-the-loop approvals, automated QA/compliance, rights tracking, micro-testing, and an analytics loop recommending next creative variants.

Primary outcomes: faster creative iteration using a catalog of trusted avatars, reduced operational overhead (no marketplace contracting by users), brand-safe and rights-compliant operations.

## 2. Goals and Non‑Goals
Goals
- Generate high-quality creative briefs from product feeds and brand rules.
- Enable two distinct flows:
  - Creator Avatar Onboarding (creators license avatars to the platform).
  - UGC generation using licensed avatars (brand users select from the curated catalog).
- Enforce brand, policy, and legal guardrails via automated QA and human checks.
- Launch micro-tests and ingest performance metrics into a recommendation loop.
- Maintain comprehensive auditability, rights windows, and platform usage permissions.

Non-Goals (MVP)
- End-user marketplace sourcing or contracting.
- On-prem media rendering (use third-party AI render providers initially).
- Cross-platform ad buying automation beyond sandbox/micro-tests.

## 3. Architecture Overview
Stack
- Frontend: Next.js (App Router), React, Tailwind/PostCSS.
- Backend: Next.js route handlers (Node runtime for media workflows).
- Agent layer: VoltAgent for supervisor/sub-agents, tools, and observability.
- Data: Postgres (Drizzle or Prisma); Redis (queues/cache).
- Storage: S3/GCS for media and documents.
- Background jobs: Vercel Queues/Background Functions or a worker service.
- Integrations: Instagram Graph API, TikTok Business, shops (Shopify/TikTok/IG Shop), rendering provider, contract/signature for creator onboarding (internal ops).

Logical Components
- Supervisor Orchestrator (state machine and guardrails).
- Sub-Agents:
  - briefGenerator
  - aiUGCRenderer
  - qaCompliance
  - launchPlanner
  - analyticsLoop
- Creator Onboarding Service (separate bounded context):
  - avatar intake, verification, licensing, rights catalog management
  - Not exposed to brand users; managed by ops/creators
- Tools/Services: approvals, rights manager, payments (for creator royalties if applicable), webhooks, media services.

Avatar Script-to-Video Pipeline (integrated)
- Preflight: claims whitelist and policy checks on script text.
- TTS: synthesize voice with SSML/prosody; obtain viseme/phoneme timings when available.
- Lip-sync: drive avatar facial animation using visemes/phonemes; vendor API or in-house model.
- Composition: add captions (burned-in or sidecar), on-screen text, product shots, safe-area-aware layouts, loudness normalization.
- Fallbacks: (1) forced alignment if visemes not provided; (2) captions-only + b-roll variant if lip-sync fails.

Operational Concerns
- Idempotent job handlers and durable state transitions.
- Event stream for UI progress (SSE or WebSockets).
- Secrets management (per‑workspace Integration records).
- Rate limit-aware API adapters.

## 4. Module and Agent Design
Supervisor: ugcOrchestrator
- Responsibilities: job routing, policy enforcement (claims whitelist, escalation), event emission, approval gating.

Sub-Agents
- briefGenerator: Extract USPs, generate angles, scripts, hooks, captions, hashtags, shot lists, disclosures. Inputs: product feed, brand rules, personas. Output: structured brief for approval.
- aiUGCRenderer: Select licensed avatar, voice, language, tone; generate variants (diverse hooks/CTAs); render jobs; cost tracking.
  - Stages:
    1) generateRenderScript(input: Brief, AICast, preferences) → RenderScript
    2) synthesizeVoice(input: RenderScript) → TTSOutput (audio + viseme/phoneme timeline if available)
    3) lipSyncAvatar(input: avatar_id, TTSOutput, model_params) → LipSyncJob (talking-head track)
    4) composeVideo(input: LipSyncJob, overlays, captions, brand layout presets, aspect_ratios[]) → CompositionJob → Asset(s)
  - Fallbacks:
    - Forced alignment for phonemes if no viseme track.
    - Captions-only composition with b-roll if lip-sync fails.
- qaCompliance: Automated checks (brand voice, typography, framing, claims, disclosures, music rights, loudness); produce QAReport and remediation suggestions.
- launchPlanner: Build LaunchBundle(s) with captions/hashtags/sound/timecodes; schedule micro-tests.
- analyticsLoop: Ingest metrics (retention, CTR, CPA/ROAS, sentiment), produce Recommendations for variants and budget shifts.

Creator Avatar Onboarding Service (internal)
- Avatar submission by human creators (self-service portal).
- Verification (KYC/identity), consent, likeness/IP license, revenue terms (if applicable).
- Reference data capture (voice samples, image/video, constraints).
- Legal docs and status lifecycle: pending_review → approved → active → suspended/expired.
- Rights catalog exposure to Orchestrator: only approved/active avatars appear in user selection.

## 5. Data Model (Logical)
Core (workspace and brand remain unchanged)
- Workspace, User, Brand, Integration

Campaign and Briefs
- Campaign, Brief (approval_status, body JSONB)

Licensed Avatars
- Creator (internal context; limited fields visible to brands)
  - id, display_name, public_bio?, status (internal)
- Avatar
  - id, creator_id, name, description, tags[], languages[], tones[], constraints, thumbnail_url
  - status (pending_review, approved, active, suspended, expired)
- AvatarLicense
  - id, avatar_id, license_terms (usage scope, platforms, whitelisting), start_at, end_at, renewal_terms, status
- Optional: AvatarAssetRef (voice model id, face/avatar model id, embeddings)

AI UGC and Pipeline Entities (new)
- AICast (references Avatar)
  - id, avatar_id, voice_id?, language_defaults[], tone_defaults[], constraints
- RenderScript
  - id, brief_id, ai_cast_id, text, ssml?, segments[], timing_prefs (speaking_rate, pitch), style/template_id, target_aspect_ratios[]
- TTSOutput
  - id, render_script_id, audio_url, format, sample_rate, viseme_track?, phoneme_track?, duration_ms
- LipSyncJob
  - id, render_script_id, avatar_id, audio_url, model_params, status (queued, processing, completed, failed), output_track_url, logs
- CompositionJob
  - id, lip_sync_job_id, overlays (captions, on_screen_text, product_shots), layout_preset, loudness_target, aspect_ratios[], status, output_asset_ids[]
- AIVariantSpec
  - id, campaign_id, brief_id, ai_cast_id, variant_count, hook_strategy, cta_strategy, length_seconds, aspect_ratios[]
- AIGenerationJob
  - id, ai_variant_spec_id, status, cost_estimate, started_at, completed_at, output_asset_ids[]

Assets and QA
- Asset, QAReport

Publishing and Testing
- LaunchBundle, Experiment, PlatformPost

Performance and Feedback
- PerformanceMetric, Comment, Recommendation

Rights, Payments, Compliance
- RightsWindow, Payment (optional creator royalties), AuditLog, Approval

Physical Modeling Notes
- RenderScript.body can store SSML; segments[] align to per-sentence timings for overlays.
- Index LipSyncJob.status and CompositionJob.status for queue dashboards.
- Keep TTSOutput duration and tracks normalized to a standard timeline base (e.g., 100 ns ticks).

## 6. API Surface and Endpoints
Public (brand user) APIs
- POST /api/campaigns
- POST /api/briefs/generate
- POST /api/briefs/:id/approve
- GET  /api/avatars (catalog of approved/active avatars with filters/tags)
- POST /api/ai-ugc/render
  - body: { briefId, aiCastId, renderScript?, variantSpec? }
  - behavior: if renderScript omitted, service generates from Brief; otherwise uses supplied SSML/script.
  - emits events: tts_started/completed, lipsync_started/completed, compose_started/completed, asset_ready
- POST /api/assets/:id/qa
- POST /api/launch/plan
- POST /api/experiments/:id/launch
- GET  /api/events/stream?jobId=...

Internal (creator onboarding) APIs/Routes
- POST /internal/creators/register
- POST /internal/avatars/submit
- POST /internal/avatars/:id/verify
- POST /internal/avatars/:id/approve
- POST /internal/licenses (create/update)
- GET  /internal/avatars/:id
- Webhooks: signature/contract status, KYC provider, rendering provider callbacks (if needed)

AuthZ
- Brand portal: workspace-scoped roles; visibility only to avatar catalog (no creator PII).
- Creator/admin portal: ops roles; manage creator KYC, avatars, licenses.

## 7. Workflows (State Machines)
A) Creator Avatar Onboarding (internal)
- creator.registered → avatar.submitted → verification_pending → approved → active
- license: draft → active → expiring_soon → expired/renewed
- On approval, avatar becomes eligible for public catalog.

B) UGC Using Licensed Avatars (brand users)
- brief: draft → pending_approval → approved
- select avatar from catalog (status=active; license valid for platform)
- ai_generation:
  - tts: queued → processing → completed/failed
  - lipsync: queued → processing → completed/failed
  - compose: queued → processing → completed/failed
  - job: queued → rendering → completed/failed (aggregates stage states)
- qa: pending → pass | fail | needs_review
- launch: bundle_ready → scheduled → posted → analyzing → archived
- recommendations: proposed → pending_approval → approved → executed

C) Rights Validation
- Pre-render: ensure AvatarLicense permits generation for requested platform/geo/duration.
- Pre-launch: RightsWindow checks for final asset-level permissions.

## 8. Compliance, Security, and Privacy
Compliance
- FTC disclosures in briefs and QA checks; platform policy validation.
- Explicit likeness/IP license captured and enforced via AvatarLicense; rights validated at render and launch.
- Escalation when license nearing expiry or usage exceeds scope.

Security
- Brand users cannot access creator PII; avatars are abstracted entities with public-safe metadata.
- Tokens encrypted at rest; signed URLs for media; strict RBAC on internal routes.
- Server-side proxying of third-party APIs; input validation via Zod schemas.

Privacy
- PII isolation in internal schema; catalog exposes only approved public fields.
- Data retention policies per workspace and per creator contract.

## 9. Scalability and Reliability
- Horizontal scaling for each stage (TTS, lip-sync, composition) with separate queues.
- Retries with backoff; DLQ for each stage; resumable composition steps.
- CDN for media delivery; Redis caching for avatar catalog; scheduled license expiry checks.
- Observability for onboarding pipeline and avatar usage telemetry.

## 10. Implementation Plan and Milestones (Updated)
M1: Briefs MVP (unchanged, 2–3 weeks)
- Campaign, Brief, Approval, AuditLog.
- briefGenerator agent; brief editor; approvals.

M2: Licensed Avatar Catalog + AI UGC (3–4 weeks)
- Internal: Creator, Avatar, AvatarLicense (admin portal MVP; minimal KYC stub).
- Public: GET /api/avatars (catalog), selection UI in AI spec flow.
- Pipeline Entities: RenderScript, TTSOutput, LipSyncJob, CompositionJob.
- AICast now bound to Avatar; AIVariantSpec, AIGenerationJob, Asset.
- aiUGCRenderer, qaCompliance base checks; render console with progress.

M3: Launch & Micro-tests (2–3 weeks)
- LaunchBundle, Experiment, PlatformPost; adapters for IG/TikTok sandbox or export.
- Pre-launch license and rights validations.

M4: Analytics Loop (2–3 weeks)
- PerformanceMetric, Recommendation; dashboards; approve → execute.

M5: Onboarding Hardening (3–4 weeks)
- Admin workflows for verification and approvals.
- License renewals, expiry notifications, optional creator royalties (Payment).
- Audit and reporting for avatar usage.

## 11. Integration Adapters (Abstraction)
- Platforms: InstagramGraphAdapter, TikTokAdapter (post, metrics, webhooks).
- Rendering: AIRenderAdapter (submit, status, assets).
- TTS: TTSAdapter with SSML support and optional viseme track.
- Lip-sync: LipSyncAdapter supporting avatar model parameters.
- Shops: ShopAdapter (Shopify/TikTok/IG shop feed).
- Internal ops: SignatureAdapter/KYC provider (for avatar onboarding) — optional MVP.

## 12. Observability and Telemetry
- Event bus with standardized events across:
  - onboarding: avatar_submitted, avatar_approved, license_activated
  - production: tts_started/completed, lipsync_started/completed, compose_started/completed, asset_ready, approval_required
  - rights: license_expiring, rights_block
- Correlation: jobId, campaignId, entityId.
- Dashboards for render durations per stage, approval time, license coverage, stage failure rates.

## 13. Risks and Mitigations
- License scope mismatch: strict validators and pre-flight checks; clear error UX.
- Avatar quality inconsistency: internal review guidelines; minimum media standards for onboarding.
- Compliance drift: periodic policy checks; template updates; escalation queues.
- Rendering costs: cost estimates and per-variant caps; approvals before large batches.
- Vendor dependence for visemes/lip-sync: keep forced-alignment and captions-only fallbacks.

## 14. Testing Strategy
- Unit: validators for AvatarLicense; RenderScript generation; SSML parsing; timing alignment.
- Integration: avatar selection → TTS → lip-sync → composition → QA → launch.
- Contract: platform posting, metrics ingestion; TTS and lip-sync adapter stubs.
- Load: per-stage queue throughput; fan-out on multi-aspect ratio renders.
- Security: RBAC isolation between brand portal and internal onboarding; signed URL tests.

## 15. Deployment and Environments
- Separate admin/creator onboarding portal routes/namespaces; shared infra.
- CI/CD, secrets management, feature flags for onboarding visibility and pipeline stages.
- Staging sandbox integrations; canary deployments per stage adapters.

## 16. Roadmap (High-Level)
- v0.1: Briefs + base avatar catalog + AI UGC with script-to-video pipeline.
- v0.2: Launch bundles + sandbox posting; base analytics.
- v0.3: Onboarding hardening; license renewals; optional royalties.
- v0.4: Advanced analytics (creative feature analysis), recommendation scoring.
- v1.0: Production hardening, quotas, billing, SOC2 readiness roadmap.

## 17. Appendix
- Prompt templates (briefs, QA, recommendations) — to be added.
- ERD with updated Avatar, License, and Pipeline entities — to be added.
- OpenAPI schema for public and internal routes — to be generated.