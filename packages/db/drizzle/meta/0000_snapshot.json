{"id": "ac8a66f5-1007-4789-aec2-deba78ad3c9e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.approvals": {"name": "approvals", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "serial", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "decided_at": {"name": "decided_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "decided_by_user_id": {"name": "decided_by_user_id", "type": "serial", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"approvals_decided_by_user_id_users_id_fk": {"name": "approvals_decided_by_user_id_users_id_fk", "tableFrom": "approvals", "tableTo": "users", "columnsFrom": ["decided_by_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "serial", "primaryKey": false, "notNull": true}, "actor_type": {"name": "actor_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "actor_id": {"name": "actor_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "target_type": {"name": "target_type", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_workspace_id_workspaces_id_fk": {"name": "audit_logs_workspace_id_workspaces_id_fk", "tableFrom": "audit_logs", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.briefs": {"name": "briefs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "campaign_id": {"name": "campaign_id", "type": "serial", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "generated_by": {"name": "generated_by", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"briefs_campaign_id_campaigns_id_fk": {"name": "briefs_campaign_id_campaigns_id_fk", "tableFrom": "briefs", "tableTo": "campaigns", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campaigns": {"name": "campaigns", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "serial", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(160)", "primaryKey": false, "notNull": true}, "objective": {"name": "objective", "type": "<PERSON><PERSON><PERSON>(80)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"campaigns_workspace_id_workspaces_id_fk": {"name": "campaigns_workspace_id_workspaces_id_fk", "tableFrom": "campaigns", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "serial", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "config_json": {"name": "config_json", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"integrations_workspace_id_workspaces_id_fk": {"name": "integrations_workspace_id_workspaces_id_fk", "tableFrom": "integrations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.memberships": {"name": "memberships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "serial", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "serial", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"memberships_workspace_id_workspaces_id_fk": {"name": "memberships_workspace_id_workspaces_id_fk", "tableFrom": "memberships", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "memberships_user_id_users_id_fk": {"name": "memberships_user_id_users_id_fk", "tableFrom": "memberships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(120)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_slug_unique": {"name": "workspaces_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}