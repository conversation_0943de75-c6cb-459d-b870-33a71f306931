/* eslint-disable @typescript-eslint/no-var-requires */
// Use CommonJS for drizzle-kit to avoid ESM loader issues in CLI
const { defineConfig } = require('drizzle-kit');
const path = require('node:path');

const schemaPath = path.resolve(__dirname, 'src', 'schema.ts');

module.exports = defineConfig({
  dialect: 'postgresql',
  schema: schemaPath,
  out: path.resolve(__dirname, 'drizzle'),
  dbCredentials: {
    url: process.env.DATABASE_URL ?? 'postgres://postgres:postgres@localhost:5432/ugc_gen',
  },
  verbose: true,
  strict: true,
});