{"name": "@ugc/db", "version": "0.1.0", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"typecheck": "tsc -p tsconfig.json --noEmit", "lint": "eslint .", "build": "tsc -p tsconfig.json", "dev": "echo 'No dev server for @ugc/db' && exit 0"}, "dependencies": {"dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "zod": "^4.0.14"}, "devDependencies": {"@types/pg": "^8.15.5", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3"}}