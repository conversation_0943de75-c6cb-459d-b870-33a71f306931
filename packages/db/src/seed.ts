import 'dotenv/config';
import { db } from './client';
import { workspaces } from './schema';
import { eq } from 'drizzle-orm';

async function main() {
  const slug = process.env.SEED_WORKSPACE_SLUG ?? 'acme';
  const name = process.env.SEED_WORKSPACE_NAME ?? 'Acme Inc';

  // drizzle-orm query builder with eq()
  const existing = await db.select().from(workspaces).where(eq(workspaces.slug, slug)).limit(1);
  if (existing.length) {
    console.log('Workspace already exists:', slug);
    return;
  }
  await db.insert(workspaces).values({ name, slug });
  console.log('Seeded workspace:', slug);
}

main().then(() => process.exit(0)).catch((e) => { console.error(e); process.exit(1); });
