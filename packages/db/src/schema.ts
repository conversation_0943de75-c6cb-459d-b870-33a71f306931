import { pgTable, serial, varchar, text, timestamp, boolean, uuid } from 'drizzle-orm/pg-core';

export const workspaces = pgTable('workspaces', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 120 }).notNull(),
  slug: varchar('slug', { length: 120 }).notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  archived: boolean('archived').default(false).notNull(),
});

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 120 }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  disabled: boolean('disabled').default(false).notNull(),
});

export const memberships = pgTable('memberships', {
  id: serial('id').primaryKey(),
  workspaceId: serial('workspace_id').notNull().references(() => workspaces.id),
  userId: serial('user_id').notNull().references(() => users.id),
  role: varchar('role', { length: 32 }).notNull(), // 'owner' | 'admin' | 'member' | 'viewer'
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

export const integrations = pgTable('integrations', {
  id: serial('id').primaryKey(),
  workspaceId: serial('workspace_id').notNull().references(() => workspaces.id),
  provider: varchar('provider', { length: 64 }).notNull(), // 'openai' | 'anthropic' | 'replicate' | 'aws' | 'gcp' | 'stripe' | 'x' | 'tiktok' | 'instagram'
  configJson: text('config_json').notNull(), // encrypted JSON
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

export const campaigns = pgTable('campaigns', {
  id: serial('id').primaryKey(),
  workspaceId: serial('workspace_id').notNull().references(() => workspaces.id),
  name: varchar('name', { length: 160 }).notNull(),
  objective: varchar('objective', { length: 80 }), // awareness | conversion | engagement
  status: varchar('status', { length: 32 }).default('draft').notNull(), // draft | active | paused | archived
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

export const briefs = pgTable('briefs', {
  id: serial('id').primaryKey(),
  campaignId: serial('campaign_id').notNull().references(() => campaigns.id),
  title: varchar('title', { length: 200 }).notNull(),
  content: text('content').notNull(), // JSON of angles/scripts/captions
  generatedBy: varchar('generated_by', { length: 64 }).default('user').notNull(), // user | agent
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

export const approvals = pgTable('approvals', {
  id: serial('id').primaryKey(),
  entityType: varchar('entity_type', { length: 64 }).notNull(), // brief | render | composition
  entityId: serial('entity_id').notNull(),
  status: varchar('status', { length: 32 }).default('pending').notNull(), // pending | approved | rejected
  note: text('note'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  decidedAt: timestamp('decided_at', { withTimezone: true }),
  decidedByUserId: serial('decided_by_user_id').references(() => users.id),
});

export const auditLogs = pgTable('audit_logs', {
  id: serial('id').primaryKey(),
  workspaceId: serial('workspace_id').notNull().references(() => workspaces.id),
  actorType: varchar('actor_type', { length: 32 }).notNull(), // user | agent | system
  actorId: varchar('actor_id', { length: 64 }).notNull(),
  action: varchar('action', { length: 128 }).notNull(),
  targetType: varchar('target_type', { length: 64 }).notNull(),
  targetId: varchar('target_id', { length: 64 }).notNull(),
  metadata: text('metadata'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});