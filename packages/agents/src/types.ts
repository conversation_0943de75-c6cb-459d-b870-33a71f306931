// Core agent types for UGC Orchestrator

export interface BriefGeneratorInput {
  product?: {
    id: string;
    title: string;
    description: string;
    images?: string[];
    price?: number;
    tags?: string[];
  };
  campaign: {
    id: number;
    name: string;
    objective: 'awareness' | 'conversion' | 'engagement';
    workspaceId: number;
  };
  reviews?: Array<{
    text: string;
    rating: number;
    verified?: boolean;
  }>;
  brandRules?: {
    voice: string;
    tone: string;
    claimsWhitelist: string[];
    prohibitedClaims: string[];
    disclosureRequirements: string[];
  };
  personas?: Array<{
    name: string;
    demographics: string;
    pains: string[];
    motivations: string[];
    channels: string[];
  }>;
  channels?: ('tiktok' | 'instagram' | 'youtube' | 'facebook')[];
}

export interface BriefGeneratorOutput {
  angles: Array<{
    id: string;
    hook: string;
    promise: string;
    painPoint?: string;
    solution?: string;
    targetPersona?: string;
  }>;
  scripts: Array<{
    id: string;
    angleId: string;
    hook: string;
    body: string;
    cta: string;
    duration?: number; // seconds
    platform?: string;
  }>;
  captions: Array<{
    id: string;
    text: string;
    platform: string;
    hashtags: string[];
    mentions?: string[];
  }>;
  hooks: Array<{
    id: string;
    text: string;
    type: 'question' | 'statement' | 'statistic' | 'story';
    platform?: string;
  }>;
  shotLists?: Array<{
    id: string;
    scriptId: string;
    shots: Array<{
      sequence: number;
      description: string;
      duration: number;
      cameraAngle?: string;
      props?: string[];
    }>;
  }>;
  hashtags: Array<{
    tag: string;
    platform: string;
    volume?: 'high' | 'medium' | 'low';
    competition?: 'high' | 'medium' | 'low';
  }>;
  disclosures: Array<{
    text: string;
    required: boolean;
    platform?: string;
    placement: 'beginning' | 'end' | 'overlay';
  }>;
  soundSuggestions?: Array<{
    id: string;
    name: string;
    mood: string;
    platform: string;
    trending?: boolean;
  }>;
}

export interface AgentRunContext {
  correlationId: string;
  userId?: number;
  workspaceId: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AgentResult<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    executionTime: number;
    tokensUsed?: number;
    model?: string;
    version?: string;
  };
}

// Base agent interface
export interface Agent<TInput, TOutput> {
  name: string;
  version: string;
  description: string;
  run(input: TInput, context: AgentRunContext): Promise<AgentResult<TOutput>>;
}

// Brief generator agent interface
export interface BriefGeneratorAgent extends Agent<BriefGeneratorInput, BriefGeneratorOutput> {
  name: 'briefGenerator';
}

// Event types for agent execution
export interface AgentEvent {
  type: 'agent.started' | 'agent.completed' | 'agent.failed' | 'agent.progress';
  agentName: string;
  correlationId: string;
  timestamp: Date;
  data?: any;
}

// Configuration for agent execution
export interface AgentConfig {
  maxRetries: number;
  timeoutMs: number;
  enableLogging: boolean;
  enableMetrics: boolean;
}
