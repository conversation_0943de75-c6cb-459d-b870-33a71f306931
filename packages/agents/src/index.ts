// UGC Orchestrator Agents Package
// 
// This package contains AI agents for content generation, approval workflows,
// and campaign orchestration. Built as a foundation for VoltAgent integration.

export * from './types.js';
export * from './briefGenerator.js';

// Re-export main agents for convenience
export { briefGenerator, BriefGenerator } from './briefGenerator.js';

// Agent registry for dynamic loading
export const AVAILABLE_AGENTS = {
  briefGenerator: 'briefGenerator',
} as const;

export type AvailableAgentNames = keyof typeof AVAILABLE_AGENTS;
