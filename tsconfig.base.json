{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "resolveJsonModule": true, "skipLibCheck": true, "noEmit": true, "jsx": "preserve", "allowJs": false, "incremental": true, "baseUrl": ".", "paths": {"@shared/*": ["packages/shared/src/*"], "@db/*": ["packages/db/src/*"], "@agents/*": ["packages/agents/src/*"]}, "types": []}, "exclude": ["node_modules"], "include": ["apps/*/src", "packages/*/src", "docs/**/*.ts"]}