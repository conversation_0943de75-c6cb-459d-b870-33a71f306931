'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function CampaignsPage() {
  const [formData, setFormData] = useState({
    name: '',
    workspaceSlug: 'acme', // Default to seeded workspace
    objective: 'awareness'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage(null);

    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: `Campaign "${result.name}" created successfully!` });
        setFormData({ name: '', workspaceSlug: 'acme', objective: 'awareness' });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to create campaign' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Campaigns</span>
            <span className="text-gray-400">→</span>
            <Link href="/briefs" className="text-blue-600 hover:text-blue-800">
              Briefs
            </Link>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Campaign</h1>
          <p className="text-gray-600">
            Set up a new UGC campaign with your objectives and target channels.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Summer Product Launch"
              />
            </div>

            <div>
              <label htmlFor="workspaceSlug" className="block text-sm font-medium text-gray-700 mb-2">
                Workspace *
              </label>
              <input
                type="text"
                id="workspaceSlug"
                name="workspaceSlug"
                value={formData.workspaceSlug}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Workspace slug"
              />
            </div>

            <div>
              <label htmlFor="objective" className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Objective
              </label>
              <select
                id="objective"
                name="objective"
                value={formData.objective}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="awareness">Awareness</option>
                <option value="conversion">Conversion</option>
                <option value="engagement">Engagement</option>
              </select>
            </div>

            <div className="flex items-center justify-between pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Campaign'}
              </button>
            </div>
          </form>

          {/* Message Display */}
          {message && (
            <div className={`mt-4 p-4 rounded-md ${
              message.type === 'success' 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message.text}
            </div>
          )}
        </div>

        {/* Next Steps */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Next Steps</h3>
          <p className="text-sm text-blue-700">
            After creating your campaign, head to the{' '}
            <Link href="/briefs" className="underline hover:no-underline">
              Briefs page
            </Link>{' '}
            to generate content briefs for your campaign.
          </p>
        </div>
      </div>
    </div>
  );
}
