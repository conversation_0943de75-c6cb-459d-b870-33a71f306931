import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="font-sans min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <header className="text-center mb-12">
          <Image
            className="mx-auto mb-8"
            src="/next.svg"
            alt="UGC Orchestrator"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            UGC Orchestrator
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create, manage, and optimize user-generated content campaigns with
            AI-powered brief generation and approval workflows.
          </p>
        </header>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">
              Campaign Management
            </h2>
            <p className="text-gray-600 mb-4">
              Set up new UGC campaigns with objectives, target channels, and
              workspace configuration.
            </p>
            <Link
              href="/campaigns"
              className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Create Campaign →
            </Link>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">
              Content Briefs
            </h2>
            <p className="text-gray-600 mb-4">
              Generate AI-powered content briefs with angles, scripts, and
              captions for your campaigns.
            </p>
            <Link
              href="/briefs"
              className="inline-flex items-center bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Manage Briefs →
            </Link>
          </div>
        </div>

        <div className="bg-gray-100 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Getting Started
          </h3>
          <ol className="text-sm text-gray-600 space-y-2">
            <li className="flex items-start">
              <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                1
              </span>
              <span>
                Create a campaign with your objectives and target workspace
              </span>
            </li>
            <li className="flex items-start">
              <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                2
              </span>
              <span>
                Generate AI-powered content briefs with angles and scripts
              </span>
            </li>
            <li className="flex items-start">
              <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                3
              </span>
              <span>Review and approve briefs to maintain content quality</span>
            </li>
          </ol>
        </div>

        <footer className="text-center text-sm text-gray-500 mt-8">
          <p>
            UGC Orchestrator MVP - Built with Next.js, Drizzle ORM, and
            PostgreSQL
          </p>
        </footer>
      </div>
    </div>
  );
}
