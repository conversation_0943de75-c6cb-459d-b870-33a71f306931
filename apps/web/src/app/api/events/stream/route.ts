import { NextRequest } from "next/server";

// GET /api/events/stream - minimal SSE stub
export async function GET(_req: NextRequest) {
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      const send = (event: string, data: unknown) => {
        controller.enqueue(encoder.encode(`event: ${event}\n`));
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
      };
      // Initial hello
      send("hello", { ok: true, ts: Date.now() });

      // heartbeat
      const interval = setInterval(() => {
        send("heartbeat", { ts: Date.now() });
      }, 10000);

      // cleanup
      // @ts-ignore
      (controller as any).closeStream = () => clearInterval(interval);
    },
    cancel() {
      // @ts-ignore
      if ((this as any).closeStream) (this as any).closeStream();
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream; charset=utf-8",
      "Cache-Control": "no-cache, no-transform",
      Connection: "keep-alive",
      "X-Accel-Buffering": "no"
    },
  });
}
