import { NextRequest } from "next/server";
import { db } from "@db/client";
import { briefs, campaigns } from "@db/schema";
import { eq } from "drizzle-orm";

// POST /api/briefs/generate
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const campaignId = Number(body?.campaignId);
    const title = String(body?.title ?? "").trim();

    if (!campaignId || !Number.isFinite(campaignId)) {
      return new Response(JSON.stringify({ error: "campaignId is required" }), {
        status: 400,
        headers: { "content-type": "application/json" },
      });
    }

    const [camp] = await db
      .select()
      .from(campaigns)
      .where(eq(campaigns.id, campaignId))
      .limit(1);
    if (!camp) {
      return new Response(JSON.stringify({ error: "Campaign not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    // Stub generation: populate angles/scripts/captions scaffold
    const generated = {
      angles: [
        {
          id: "a1",
          hook: "Problem -> Solution",
          promise: "Show how product solves X",
        },
        { id: "a2", hook: "Before/After", promise: "Transformation in 15s" },
      ],
      scripts: [
        {
          id: "s1",
          angleId: "a1",
          lines: ["Hook line...", "Body line...", "CTA line..."],
        },
      ],
      captions: [{ id: "c1", text: "Try it today 🚀", platform: "tiktok" }],
    };

    const [created] = await db
      .insert(briefs)
      .values({
        campaignId: camp.id,
        title: title || `Auto Brief for ${camp.name}`,
        content: JSON.stringify(generated),
        generatedBy: "agent",
      })
      .returning();

    return new Response(
      JSON.stringify({
        id: created.id,
        campaignId: created.campaignId,
        title: created.title,
      }),
      {
        status: 201,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("POST /api/briefs/generate error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}
