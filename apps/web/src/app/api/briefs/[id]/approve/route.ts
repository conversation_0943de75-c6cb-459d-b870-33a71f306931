import { NextRequest } from "next/server";
import { db } from "../../../../../../../../packages/db/src/client";
import { approvals } from "../../../../../../../../packages/db/src/schema";
import { z } from "zod";

// POST /api/briefs/:id/approve
const bodySchema = z.object({
  status: z.enum(["approved", "rejected"]),
  note: z.string().max(500).optional(),
  userId: z.number().int().optional(), // placeholder for auth wiring
});

export async function POST(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const briefId = Number(params.id);
    if (!briefId || !Number.isFinite(briefId)) {
      return new Response(JSON.stringify({ error: "Invalid brief id" }), { status: 400, headers: { "content-type": "application/json" } });
    }

    const json = await _req.json();
    const parsed = bodySchema.safeParse(json);
    if (!parsed.success) {
      return new Response(JSON.stringify({ error: "Invalid body", details: parsed.error.flatten() }), { status: 400, headers: { "content-type": "application/json" } });
    }

    const { status, note, userId } = parsed.data;
    const [created] = await db.insert(approvals).values({
      entityType: "brief",
      entityId: briefId,
      status,
      note: note ?? null,
      decidedByUserId: userId ?? null,
    }).returning();

    return new Response(JSON.stringify({ id: created.id, status: created.status }), {
      status: 201,
      headers: { "content-type": "application/json" },
    });
  } catch (err: any) {
    console.error("POST /api/briefs/:id/approve error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), { status: 500, headers: { "content-type": "application/json" } });
  }
}
