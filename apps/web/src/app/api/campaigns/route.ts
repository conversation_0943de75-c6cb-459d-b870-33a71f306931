import { NextRequest } from "next/server";
import { db } from "@db/client";
import { campaigns, workspaces } from "@db/schema";
import { eq } from "drizzle-orm";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const name = String(body?.name ?? "").trim();
    const workspaceSlug = String(body?.workspaceSlug ?? "").trim();

    if (!name || !workspaceSlug) {
      return new Response(
        JSON.stringify({ error: "name and workspaceSlug are required" }),
        {
          status: 400,
          headers: { "content-type": "application/json" },
        }
      );
    }

    const ws = await db.query.workspaces.findFirst({
      where: eq(workspaces.slug, workspaceSlug),
    });

    if (!ws) {
      return new Response(JSON.stringify({ error: "Workspace not found" }), {
        status: 404,
        headers: { "content-type": "application/json" },
      });
    }

    const [created] = await db
      .insert(campaigns)
      .values({
        workspaceId: ws.id,
        name,
        objective: body?.objective ?? null,
      })
      .returning();

    return new Response(
      JSON.stringify({
        id: created.id,
        name: created.name,
        status: created.status,
      }),
      {
        status: 201,
        headers: { "content-type": "application/json" },
      }
    );
  } catch (err: any) {
    console.error("POST /api/campaigns error", err);
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "content-type": "application/json" },
    });
  }
}
