'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Brief {
  id: number;
  campaignId: number;
  title: string;
  content: string;
  generatedBy: string;
  createdAt: string;
}

export default function BriefsPage() {
  const [briefs, setBriefs] = useState<Brief[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isApproving, setIsApproving] = useState<number | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // SSE Connection
  useEffect(() => {
    const es = new EventSource('/api/events/stream');
    setEventSource(es);
    setConnectionStatus('connecting');

    es.onopen = () => {
      setConnectionStatus('connected');
    };

    es.onmessage = (event) => {
      console.log('SSE message:', event.data);
    };

    es.addEventListener('hello', (event) => {
      console.log('SSE hello:', event.data);
    });

    es.addEventListener('heartbeat', (event) => {
      console.log('SSE heartbeat:', event.data);
    });

    es.onerror = () => {
      setConnectionStatus('disconnected');
    };

    return () => {
      es.close();
      setEventSource(null);
      setConnectionStatus('disconnected');
    };
  }, []);

  const generateBrief = async () => {
    setIsGenerating(true);
    setMessage(null);

    try {
      const response = await fetch('/api/briefs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaignId: 1, // Default to first campaign for MVP
          title: 'UGC Concepts v0'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: `Brief "${result.title}" generated successfully!` });
        // Add the new brief to the list
        const newBrief: Brief = {
          id: result.id,
          campaignId: result.campaignId,
          title: result.title,
          content: '{}', // Will be populated when we fetch briefs
          generatedBy: 'agent',
          createdAt: new Date().toISOString()
        };
        setBriefs(prev => [newBrief, ...prev]);
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to generate brief' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsGenerating(false);
    }
  };

  const approveBrief = async (briefId: number, status: 'approved' | 'rejected') => {
    setIsApproving(briefId);
    setMessage(null);

    try {
      const response = await fetch(`/api/briefs/${briefId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          note: status === 'approved' ? 'Looks good' : 'Needs revision',
          userId: 1 // Placeholder for auth
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `Brief ${status === 'approved' ? 'approved' : 'rejected'} successfully!` 
        });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update approval status' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setIsApproving(null);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'disconnected': return 'text-red-600';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Navigation */}
        <nav className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Home
            </Link>
            <span className="text-gray-400">→</span>
            <Link href="/campaigns" className="text-blue-600 hover:text-blue-800">
              Campaigns
            </Link>
            <span className="text-gray-400">→</span>
            <span className="text-gray-900 font-medium">Briefs</span>
          </div>
        </nav>

        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Content Briefs</h1>
            <p className="text-gray-600">
              Generate and manage AI-powered content briefs for your campaigns.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className={`text-sm ${getConnectionStatusColor()}`}>
              ● {connectionStatus === 'connected' ? 'Live' : connectionStatus}
            </div>
            <button
              onClick={generateBrief}
              disabled={isGenerating}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? 'Generating...' : 'Generate Brief'}
            </button>
          </div>
        </div>

        {/* Message Display */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}

        {/* Briefs List */}
        <div className="space-y-4">
          {briefs.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <p className="text-gray-500 mb-4">No briefs generated yet.</p>
              <button
                onClick={generateBrief}
                disabled={isGenerating}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {isGenerating ? 'Generating...' : 'Generate Your First Brief'}
              </button>
            </div>
          ) : (
            briefs.map((brief) => (
              <div key={brief.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{brief.title}</h3>
                    <p className="text-sm text-gray-500">
                      Campaign ID: {brief.campaignId} • Generated by: {brief.generatedBy}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => approveBrief(brief.id, 'approved')}
                      disabled={isApproving === brief.id}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      {isApproving === brief.id ? 'Processing...' : 'Approve'}
                    </button>
                    <button
                      onClick={() => approveBrief(brief.id, 'rejected')}
                      disabled={isApproving === brief.id}
                      className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      {isApproving === brief.id ? 'Processing...' : 'Reject'}
                    </button>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <p>Created: {new Date(brief.createdAt).toLocaleString()}</p>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Info Panel */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">How it works</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Click "Generate Brief" to create AI-powered content briefs</li>
            <li>• Review the generated angles, scripts, and captions</li>
            <li>• Approve or reject briefs to control content quality</li>
            <li>• Live connection shows real-time updates via Server-Sent Events</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
